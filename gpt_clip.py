import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
import win32clipboard
from PIL import Image
import io


class ScreenshotManager:
    def __init__(self):
        self.hotkey_detected = False
        self.copy_screenshots_detected = False
        self.running = True
        self.listener = None
        self.root = None

        # ShareX output directory - only monitor your specific path
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        self.temp_dir = Path(tempfile.gettempdir()) / "screenshot_manager"
        self.temp_dir.mkdir(exist_ok=True)

        # File monitoring
        self.file_observer = None
        self.latest_file = None
        self.processing_lock = threading.Lock()
        self.collected_screenshots = []
        self.screenshot_lock = threading.Lock()

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()



    def setup_root(self):
        """Setup the persistent root window."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the root window
        self.root.title("Screenshot Manager")

    def setup_file_monitoring(self):
        """Setup file system monitoring for ShareX output."""
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created ShareX directory: {self.sharex_dir}")
        else:
            print(f"✓ Found ShareX directory: {self.sharex_dir}")

        self.file_handler = ShareXFileHandler(self)
        self.file_observer = Observer()

        # Monitor only the specific ShareX directory
        self.file_observer.schedule(self.file_handler, str(self.sharex_dir), recursive=False)
        self.file_observer.start()

    def show_status_bubble(self, message):
        """Show a status bubble with the given message."""
        def create_bubble():
            bubble = tk.Toplevel(self.root)
            bubble.title("Screenshot Manager")
            bubble.overrideredirect(True)
            bubble.attributes('-topmost', True)
            bubble.attributes('-alpha', 0.95)

            # Get screen dimensions and define max bubble size
            screen_width = bubble.winfo_screenwidth()
            screen_height = bubble.winfo_screenheight()
            max_width = int(screen_width * 0.4)
            max_height = int(screen_height * 0.3)

            # Main frame with styling
            outer_frame = tk.Frame(bubble, bg='#2E86AB', relief='raised', bd=3)
            outer_frame.pack(padx=10, pady=10)
            inner_frame = tk.Frame(outer_frame, bg='#A23B72', relief='flat', bd=2)
            inner_frame.pack(padx=5, pady=5)

            # Status message label
            status_label = tk.Label(
                inner_frame,
                text=f"📋 {message.strip()}",
                font=('Arial', 14),
                fg='white',
                bg='#A23B72',
                padx=20,
                pady=20,
                justify='left',
                anchor='nw',
                wraplength=max_width - 60
            )
            status_label.pack(expand=True, fill='both')

            def close_bubble():
                try:
                    bubble.destroy()
                except:
                    pass

            # Update the window to calculate its required size
            bubble.update_idletasks()

            # Get the required size and constrain it to the max dimensions
            req_width = bubble.winfo_reqwidth()
            req_height = bubble.winfo_reqheight()

            final_width = min(req_width, max_width)
            final_height = min(req_height, max_height)

            # Position bubble
            x = 20
            y = 200
            bubble.geometry(f"{final_width}x{final_height}+{x}+{y}")

            # Auto-close after 5 seconds
            bubble.after(5000, close_bubble)

        if self.root:
            self.root.after(0, create_bubble)


    def on_key_press(self, key):
        """Handle key press events."""
        try:
            # Detect Ctrl+D
            if hasattr(key, 'char') and key.char == '\x04':
                print("Ctrl+D detected!")
                self.hotkey_detected = True
            elif hasattr(key, 'char') and key.char == '\x18':
                print("Ctrl+X detected!")
                self.copy_screenshots_detected = True
        except Exception:
            pass

    def check_hotkey(self):
        """Check for hotkey detection."""
        if self.hotkey_detected:
            self.hotkey_detected = False
            print("Launching ShareX for screenshot capture...")
            self.launch_sharex_capture()
        if self.copy_screenshots_detected:
            self.copy_screenshots_detected = False
            print("Ctrl+X detected! Copying all collected screenshots to clipboard...")
            self.copy_screenshots_to_clipboard()
        if self.running:
            self.root.after(100, self.check_hotkey)

    def start_listener(self):
        """Start the keyboard listener."""
        print("Screenshot Manager - ShareX Integration")
        print("=" * 40)
        print("Press Ctrl+D to capture screenshot")
        print("Press Ctrl+X to copy all screenshots to clipboard")
        print("Press Ctrl+C to exit")
        print()
        print(f"📁 Monitoring ShareX directory: {self.sharex_dir}")

        self.listener = keyboard.Listener(on_press=self.on_key_press)
        self.listener.start()

        # Start checking for hotkeys
        self.check_hotkey()

        # Start tkinter main loop
        try:
            self.root.mainloop()
        finally:
            if self.listener:
                self.listener.stop()
            if self.file_observer:
                self.file_observer.stop()
                self.file_observer.join()

    def launch_sharex_capture(self):
        """Launch ShareX region capture using CLI hotkey actions."""
        try:
            cmd = ['ShareX', '-RectangleRegion']
            subprocess.Popen(cmd)
            print("✓ ShareX rectangle region capture launched")

        except FileNotFoundError:
            print("✗ ShareX not found in PATH")
        except Exception as e:
            print(f"Error launching ShareX: {e}")

    def copy_image_to_clipboard(self, image_path):
        """Copy a single image to clipboard."""
        try:
            # Open and convert image to bitmap format for clipboard
            image = Image.open(image_path)

            # Convert to RGB if necessary (for JPEG compatibility)
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Save to memory as bitmap
            output = io.BytesIO()
            image.save(output, format='BMP')
            data = output.getvalue()[14:]  # Remove BMP header for clipboard
            output.close()

            # Copy to clipboard
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
            win32clipboard.CloseClipboard()

            return True
        except Exception as e:
            print(f"Error copying image to clipboard: {e}")
            return False

    def copy_screenshots_to_clipboard(self):
        """Copy all collected screenshots to clipboard and delete source files."""
        with self.screenshot_lock:
            if not self.collected_screenshots:
                print("❌ No screenshots collected. Take some screenshots first with Ctrl+D.")
                self.show_status_bubble("No screenshots to copy!")
                return

            try:
                print(f"� Copying {len(self.collected_screenshots)} screenshot(s) to clipboard...")

                # If only one screenshot, copy it directly
                if len(self.collected_screenshots) == 1:
                    success = self.copy_image_to_clipboard(self.collected_screenshots[0])
                    if success:
                        print("✅ Screenshot copied to clipboard!")
                        self.show_status_bubble("Screenshot copied to clipboard!")
                    else:
                        print("❌ Failed to copy screenshot to clipboard")
                        self.show_status_bubble("Failed to copy screenshot!")
                        return
                else:
                    # For multiple screenshots, copy the first one (you could modify this to combine them)
                    success = self.copy_image_to_clipboard(self.collected_screenshots[0])
                    if success:
                        print(f"✅ First screenshot copied to clipboard! ({len(self.collected_screenshots)} total)")
                        self.show_status_bubble(f"First of {len(self.collected_screenshots)} screenshots copied!")
                    else:
                        print("❌ Failed to copy screenshots to clipboard")
                        self.show_status_bubble("Failed to copy screenshots!")
                        return

                # Delete all source files after successful copy
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                        print(f"🗑️ Deleted: {os.path.basename(screenshot_path)}")
                    except Exception as e:
                        print(f"⚠ Could not delete temp image: {e}")

                self.collected_screenshots.clear()
                print("🔄 Ready for next screenshot sequence (press Ctrl+D to start capturing)")

            except Exception as e:
                print(f"❌ Error copying screenshots to clipboard: {e}")
                self.show_status_bubble("Error copying screenshots!")
                # Clean up on error
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                    except:
                        pass
                self.collected_screenshots.clear()

    def process_new_image(self, image_path):
        """Process newly captured image by storing it for batch processing."""
        with self.screenshot_lock:
            try:
                print(f"📷 New screenshot detected: {image_path}")
                time.sleep(0.5)
                if not os.path.exists(image_path):
                    print("❌ Image file not found"); return
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                temp_filename = f"screenshot_{len(self.collected_screenshots)+1:03d}_{timestamp}.png"
                temp_path = self.temp_dir / temp_filename
                shutil.copy2(image_path, temp_path)
                self.collected_screenshots.append(str(temp_path))
                print(f"📸 Screenshot {len(self.collected_screenshots)} stored. Press Ctrl+X to copy all screenshots to clipboard.")
                try:
                    os.remove(image_path)
                    print(f"🗑️ Cleaned up original screenshot: {os.path.basename(image_path)}")
                except Exception as e:
                    print(f"⚠ Could not delete original image: {e}")
            except Exception as e:
                print(f"❌ Error storing image: {e}")
                if os.path.exists(image_path):
                    try: os.remove(image_path)
                    except: pass


class ShareXFileHandler(FileSystemEventHandler):
    """File system event handler for ShareX screenshots."""

    def __init__(self, manager):
        self.manager = manager
        self.last_processed = None

    def on_created(self, event):
        if event.is_directory:
            return

        file_path = event.src_path

        if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            if file_path != self.last_processed:
                self.last_processed = file_path
                threading.Thread(
                    target=self.manager.process_new_image,
                    args=(file_path,),
                    daemon=True
                ).start()


def main():
    """Main function."""
    try:
        print("Screenshot Manager - ShareX Integration")
        print("=" * 40)

        try:
            import watchdog, win32clipboard
            from PIL import Image
            print("✓ All dependencies found")
        except ImportError as e:
            print(f"✗ Missing dependency: {e.name}")
            print("Please run: pip install watchdog pywin32 pillow")
            return

        try:
            subprocess.run(['ShareX', '--version'], capture_output=True, timeout=3, check=True)
            print("✓ ShareX found and accessible")
        except Exception:
            print("⚠ ShareX may not be in PATH or is not installed.")

        print()

        manager = ScreenshotManager()
        manager.start_listener()

    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()